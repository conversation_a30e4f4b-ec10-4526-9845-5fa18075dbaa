# 小车循迹工程接线纠正指导文档

## 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-31
- **负责人**: Emma (产品经理)
- **审核人**: Mike (团队领袖)
- **适用项目**: 小车循迹控制系统

---

## ⚠️ 重要提醒

**本文档基于代码配置分析，提供标准接线方案。请严格按照此文档进行接线纠正，确保硬件与软件配置完全匹配。**

---

## 📋 接线纠正对比表

### 🔴 需要纠正的接线

| 模块 | 信号线 | 原接线文档 | **正确接线** | 纠正说明 |
|------|--------|------------|-------------|----------|
| **右轮编码器** | A相 | A26 | **A25** | ❌ 需要改接 |
| **右轮编码器** | B相 | A25 | **A26** | ❌ 需要改接 |
| **左轮编码器** | A相 | B24 | **B20** | ❌ 需要改接 |
| **左轮编码器** | B相 | B20 | **B24** | ❌ 需要改接 |

### ✅ 接线正确的模块

| 模块 | 信号线 | 接线 | 状态 |
|------|--------|------|------|
| **MPU6050** | SCL | A1 | ✅ 正确 |
| **MPU6050** | SDA | A0 | ✅ 正确 |
| **MPU6050** | INT | A7 | ✅ 正确 |
| **OLED屏幕** | SCL | A28 | ✅ 正确 |
| **OLED屏幕** | SDA | A31 | ✅ 正确 |
| **OLED屏幕** | RES | B14 | ✅ 正确 |
| **OLED屏幕** | DC | B15 | ✅ 正确 |

---

## 🔧 详细纠正步骤

### 第一步：编码器接线纠正

#### 右轮编码器纠正
```
原接线：
├── A相 → A26 (错误)
└── B相 → A25 (错误)

正确接线：
├── A相 → A25 ✅
└── B相 → A26 ✅
```

#### 左轮编码器纠正
```
原接线：
├── A相 → B24 (错误)
└── B相 → B20 (错误)

正确接线：
├── A相 → B20 ✅
└── B相 → B24 ✅
```

### 第二步：验证其他模块接线

#### MPU6050 (无需修改)
```
VCC → 5V (电源模块1)
GND → GND (电源模块1)
SCL → A1 ✅
SDA → A0 ✅
INT → A7 ✅
```

#### OLED屏幕 (无需修改)
```
VCC → 3.3V (电源模块1)
GND → GND (电源模块1)
SCL → A28 ✅
SDA → A31 ✅
RES → B14 ✅
DC → B15 ✅
```

---

## 📊 完整接线配置表

### 电源系统
| 模块 | 电源连接 | 说明 |
|------|----------|------|
| **电源模块1** | 电池正负极 | 为MCU、传感器、电机驱动供电 |
| **电源模块2** | 电池正负极 | 为K210、舵机供电 |

### 传感器系统
| 传感器 | 引脚 | MCU连接 | 电源 |
|--------|------|---------|------|
| **MPU6050** | VCC | 5V | 电源模块1 |
| | GND | GND | 电源模块1 |
| | SCL | A1 | - |
| | SDA | A0 | - |
| | INT | A7 | - |
| **灰度传感器** | OUT1 | A9 | 电源模块1 |
| | OUT3 | A27 | 电源模块1 |
| | OUT4 | A24 | 电源模块1 |
| | OUT5 | B16 | 电源模块1 |
| | OUT6 | A12 | 电源模块1 |
| | OUT8 | B6 | 电源模块1 |

### 编码器系统 (⚠️ 重点纠正)
| 编码器 | 信号 | **正确连接** | 原错误连接 |
|--------|------|-------------|------------|
| **右轮编码器** | A相 | **A25** | ~~A26~~ |
| | B相 | **A26** | ~~A25~~ |
| **左轮编码器** | A相 | **B20** | ~~B24~~ |
| | B相 | **B24** | ~~B20~~ |

### 电机控制系统
| TB6612引脚 | MCU连接 | 说明 |
|------------|---------|------|
| PWMA | B2 | 右轮PWM |
| AIN1 | A13 | 右轮方向1 |
| AIN2 | A14 | 右轮方向2 |
| PWMB | B3 | 左轮PWM |
| BIN1 | A16 | 左轮方向1 |
| BIN2 | A17 | 左轮方向2 |
| STBY | 5V | 使能信号 |

### 显示系统
| OLED引脚 | MCU连接 | 说明 |
|----------|---------|------|
| VCC | 3.3V | 电源 |
| GND | GND | 地线 |
| SCL | A28 | 时钟线 |
| SDA | A31 | 数据线 |
| RES | B14 | 复位线 |
| DC | B15 | 数据/命令选择 |

### 按键系统
| 按键 | MCU连接 | 触发条件 |
|------|---------|----------|
| 按键1 | A8 | 接GND表示按下 |
| 按键2 | B7 | 接3.3V表示按下 |

---

## ⚡ 纠正后的功能验证

### 编码器功能验证
1. **右轮编码器测试**
   - A相 (A25) 应能正确检测转动
   - B相 (A26) 应能正确检测方向
   
2. **左轮编码器测试**
   - A相 (B20) 应能正确检测转动
   - B相 (B24) 应能正确检测方向

### 系统集成验证
1. **平衡控制验证**
   - MPU6050姿态数据正常
   - 编码器速度反馈正常
   - PID控制响应正常

2. **循迹功能验证**
   - 灰度传感器检测正常
   - 转向控制响应正常
   - 速度控制稳定

---

## 🚨 注意事项

### 安全提醒
1. **断电操作**: 所有接线修改必须在断电状态下进行
2. **防短路**: 确认接线正确后再通电测试
3. **分步验证**: 修改一个模块后立即验证功能

### 常见错误
1. **编码器相位错误**: A相B相接反会导致方向判断错误
2. **电源混接**: 3.3V和5V不能混接
3. **信号线松动**: 确保所有连接牢固可靠

### 故障排查
1. **编码器无信号**: 检查A相B相是否接到正确引脚
2. **方向判断错误**: 检查A相B相是否接反
3. **系统不稳定**: 检查电源和地线连接

---

## 📝 纠正检查清单

### 纠正前检查
- [ ] 确认当前接线状态
- [ ] 准备必要的工具和线材
- [ ] 系统完全断电

### 纠正过程检查
- [ ] 右轮编码器A相：A26 → A25
- [ ] 右轮编码器B相：A25 → A26
- [ ] 左轮编码器A相：B24 → B20
- [ ] 左轮编码器B相：B20 → B24
- [ ] 所有连接牢固可靠

### 纠正后验证
- [ ] 编码器信号正常
- [ ] 电机控制正常
- [ ] 平衡功能正常
- [ ] 循迹功能正常
- [ ] 系统整体稳定

---

## 📞 技术支持

如在接线纠正过程中遇到问题，请联系技术团队：
- **项目负责人**: Mike (团队领袖)
- **硬件负责人**: Alex (工程师)
- **文档维护**: Emma (产品经理)

---

**文档结束**

*本文档基于代码配置分析生成，确保硬件接线与软件配置完全匹配。*
