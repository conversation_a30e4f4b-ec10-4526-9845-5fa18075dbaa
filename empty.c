/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "ti_msp_dl_config.h"

#include "bsp_debugtimer.h"
#include "bsp_systick.h"
//#include "bsp_oled.h"     // 暂时注释掉OLED相关
//#include "bsp_siic.h"     // 暂时注释掉I2C相关
//#include "balance.h"      // 暂时注释掉平衡控制相关
#include "bsp_printf.h"

//#include "bluetooth.h"
//#include "show.h"        // 暂时注释掉OLED显示相关
//#include "MPU6050.h"     // 暂时注释掉MPU6050相关
// 电机控制变量 - 直接定义，不依赖balance.c
int Turn1 = 0;
int Velocity1 = 0;
uint8_t Flag_Stop = 0;

// 测试变量
int KEY0 = 1;
static int motor_test_state = 0;

// 简单的电机控制函数
void Motor_Control(int velocity, int turn)
{
    int Motor_Left = velocity - turn;
    int Motor_Right = velocity + turn;

    // 限制PWM值范围
    if(Motor_Left > 100) Motor_Left = 100;
    if(Motor_Left < -100) Motor_Left = -100;
    if(Motor_Right > 100) Motor_Right = 100;
    if(Motor_Right < -100) Motor_Right = -100;

    // 左轮控制 (B端)
    if(Motor_Left >= 0) {
        // 正转
        DL_GPIO_clearPins(MOTOR_DIR_BIN1_PORT, MOTOR_DIR_BIN1_PIN);
        DL_GPIO_setPins(MOTOR_DIR_BIN2_PORT, MOTOR_DIR_BIN2_PIN);
        DL_TimerA_setCaptureCompareValue(PWM_0_INST, Motor_Left, DL_TIMER_CC_1_INDEX);
    } else {
        // 反转
        DL_GPIO_setPins(MOTOR_DIR_BIN1_PORT, MOTOR_DIR_BIN1_PIN);
        DL_GPIO_clearPins(MOTOR_DIR_BIN2_PORT, MOTOR_DIR_BIN2_PIN);
        DL_TimerA_setCaptureCompareValue(PWM_0_INST, -Motor_Left, DL_TIMER_CC_1_INDEX);
    }

    // 右轮控制 (A端)
    if(Motor_Right >= 0) {
        // 正转
        DL_GPIO_clearPins(MOTOR_DIR_AIN1_PORT, MOTOR_DIR_AIN1_PIN);
        DL_GPIO_setPins(MOTOR_DIR_AIN2_PORT, MOTOR_DIR_AIN2_PIN);
        DL_TimerA_setCaptureCompareValue(PWM_0_INST, Motor_Right, DL_TIMER_CC_0_INDEX);
    } else {
        // 反转
        DL_GPIO_setPins(MOTOR_DIR_AIN1_PORT, MOTOR_DIR_AIN1_PIN);
        DL_GPIO_clearPins(MOTOR_DIR_AIN2_PORT, MOTOR_DIR_AIN2_PIN);
        DL_TimerA_setCaptureCompareValue(PWM_0_INST, -Motor_Right, DL_TIMER_CC_0_INDEX);
    }
}
int main(void)
{
    SYSCFG_DL_init();

    // 启动调试定时器
    DL_TimerG_startCounter(DebugTimer_INST);

    // 启动PWM定时器 - 电机控制必需
    DL_TimerA_startCounter(PWM_0_INST);

    // 串口调试中断 - 保留用于调试
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);

    uint32_t LEDTick = 0;

    // 初始化电机状态
    Turn1 = 0;
    Velocity1 = 20;  // 默认前进速度
    Flag_Stop = 0;

    while (1) {
        // 电机控制逻辑
        if(!Flag_Stop) {
            Motor_Control(Velocity1, Turn1);
        } else {
            Motor_Control(0, 0);  // 停止
        }

        // 100ms任务
        if(((LEDTick-Systick_getTick())&SysTickMAX_COUNT) >= SysTick_MS(100)) {
            // 读取按键状态
            KEY0 = DL_GPIO_readPins(GPIO_GRP_0_KEY0_PORT, GPIO_GRP_0_KEY0_PIN);

            // LED闪烁表示系统正常
            DL_GPIO_togglePins(LED_PORT, LED_UserLED_PIN);
            LEDTick = Systick_getTick();

            // 按键控制电机测试
            if(KEY0 == 0) {
                motor_test_state++;
                if(motor_test_state > 5) motor_test_state = 0;

                switch(motor_test_state) {
                    case 0:
                        Velocity1 = 0; Turn1 = 0; Flag_Stop = 1;
                        break;    // 停止
                    case 1:
                        Velocity1 = 30; Turn1 = 0; Flag_Stop = 0;
                        break;   // 前进
                    case 2:
                        Velocity1 = -30; Turn1 = 0; Flag_Stop = 0;
                        break;  // 后退
                    case 3:
                        Velocity1 = 0; Turn1 = 40; Flag_Stop = 0;
                        break;   // 左转
                    case 4:
                        Velocity1 = 0; Turn1 = -40; Flag_Stop = 0;
                        break;  // 右转
                    case 5:
                        Velocity1 = 20; Turn1 = 20; Flag_Stop = 0;
                        break;  // 前进+左转
                }

                // 防抖延时
                delay_ms(300);
            }
        }
    }
}

// 外部中断处理 - 暂时注释掉所有编码器和MPU6050中断
/*
void GROUP1_IRQHandler(void)
{
    // 编码器和MPU6050中断处理已注释掉
}
*/


//串口0,用于调试
void UART_0_INST_IRQHandler(void)
{
    switch (DL_UART_Main_getPendingInterrupt(UART_0_INST)) 
    {
        case DL_UART_IIDX_RX:
            DL_UART_transmitData(UART_0_INST,DL_UART_Main_receiveData(UART_0_INST));
            break;
        default:
            break;
    }
		
}
// 所有不需要的函数已删除，保持代码简洁