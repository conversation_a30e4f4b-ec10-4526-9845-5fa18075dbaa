# 小车循迹控制系统 - 完整接线配置报告

## 📋 文档信息
- **版本**: v2.0 (最终正确版本)
- **创建日期**: 2025-01-31
- **负责人**: Emma (产品经理)
- **审核人**: Mike (团队领袖)
- **适用项目**: 小车循迹控制系统
- **状态**: ✅ **已验证正确配置**

---

## 🎯 配置说明

**本文档提供小车循迹控制系统的标准接线配置，所有配置均已与代码完全匹配验证。按此文档接线可确保系统正常运行。**

---

## 📊 完整系统接线配置表

### 🔄 **编码器系统**
| 编码器 | 信号 | MCU引脚 | 功能 | 状态 |
|--------|------|---------|------|------|
| **右轮编码器** | A相 | **A25** | 编码器输入 | ✅ 正确 |
| **右轮编码器** | B相 | **A26** | 编码器输入 | ✅ 正确 |
| **左轮编码器** | A相 | **B20** | 编码器输入 | ✅ 正确 |
| **左轮编码器** | B相 | **B24** | 编码器输入 | ✅ 正确 |

### ⚡ **TB6612电机驱动系统**

#### **控制输入引脚 (MCU → TB6612)**
| TB6612引脚 | MCU引脚 | 功能 | 状态 |
|------------|---------|------|------|
| **PWMA** | **B2** | 右轮PWM控制 | ✅ 正确 |
| **AIN1** | **A16** | 右轮方向控制1 | ✅ 正确 |
| **AIN2** | **A17** | 右轮方向控制2 | ✅ 正确 |
| **PWMB** | **B3** | 左轮PWM控制 | ✅ 正确 |
| **BIN1** | **A14** | 左轮方向控制1 | ✅ 正确 |
| **BIN2** | **A13** | 左轮方向控制2 | ✅ 正确 |
| **STBY** | **5V** | 使能信号 | ✅ 正确 |

#### **功率输出引脚 (TB6612 → 电机)**
| TB6612输出 | 连接对象 | 功能 | 状态 |
|------------|----------|------|------|
| **AO1** | 右轮电机正极 | 右轮功率输出+ | ✅ 正确 |
| **AO2** | 右轮电机负极 | 右轮功率输出- | ✅ 正确 |
| **BO1** | 左轮电机正极 | 左轮功率输出+ | ✅ 正确 |
| **BO2** | 左轮电机负极 | 左轮功率输出- | ✅ 正确 |

### 📡 **MPU6050陀螺仪加速度计**
| MPU6050引脚 | MCU引脚 | 功能 | 状态 |
|-------------|---------|------|------|
| **SCL** | **A1** | I2C时钟线 | ✅ 正确 |
| **SDA** | **A0** | I2C数据线 | ✅ 正确 |
| **INT** | **A7** | 中断信号 | ✅ 正确 |
| **VCC** | **3.3V** | 电源正极 | ✅ 正确 |
| **GND** | **GND** | 电源负极 | ✅ 正确 |

### 📺 **OLED显示屏**
| OLED引脚 | MCU引脚 | 功能 | 状态 |
|----------|---------|------|------|
| **SCL** | **A28** | I2C时钟线 | ✅ 正确 |
| **SDA** | **A31** | I2C数据线 | ✅ 正确 |
| **RES** | **B14** | 复位信号 | ✅ 正确 |
| **DC** | **B15** | 数据/命令选择 | ✅ 正确 |
| **VCC** | **3.3V** | 电源正极 | ✅ 正确 |
| **GND** | **GND** | 电源负极 | ✅ 正确 |

### 🛤️ **循迹传感器系统**
| 传感器 | MCU引脚 | 功能 | 状态 |
|--------|---------|------|------|
| **循迹传感器1** | **A2** | 数字输入 | ✅ 正确 |
| **循迹传感器2** | **A3** | 数字输入 | ✅ 正确 |
| **循迹传感器3** | **A4** | 数字输入 | ✅ 正确 |
| **循迹传感器4** | **A5** | 数字输入 | ✅ 正确 |
| **循迹传感器5** | **A6** | 数字输入 | ✅ 正确 |

### 🔧 **其他系统引脚**
| 功能 | MCU引脚 | 说明 | 状态 |
|------|---------|------|------|
| **用户LED** | **B9** | 状态指示灯 | ✅ 正确 |
| **用户按键** | **A18** | 用户输入 | ✅ 正确 |
| **电压检测** | **A15** | ADC电压监测 | ✅ 正确 |
| **调试UART TX** | **A10** | 串口发送 | ✅ 正确 |
| **调试UART RX** | **A11** | 串口接收 | ✅ 正确 |

---

## 🔌 详细接线指南

### **第一步：电源系统接线**
```
电源分配：
├── MCU供电：3.3V
├── 电机供电：5V-12V (根据电机规格)
├── 传感器供电：3.3V
├── TB6612逻辑供电：5V
└── 共地：所有GND连接
```

### **第二步：编码器接线**
```
右轮编码器：
├── VCC → 3.3V
├── GND → GND
├── A相 → MCU A25
└── B相 → MCU A26

左轮编码器：
├── VCC → 3.3V
├── GND → GND
├── A相 → MCU B20
└── B相 → MCU B24
```

### **第三步：TB6612电机驱动接线**
```
TB6612控制引脚：
├── PWMA → MCU B2
├── AIN1 → MCU A16
├── AIN2 → MCU A17
├── PWMB → MCU B3
├── BIN1 → MCU A14
├── BIN2 → MCU A13
└── STBY → 5V (直接供电)

TB6612电源引脚：
├── VCC → 5V (逻辑电源)
├── VM → 电机电源 (5V-12V)
└── GND → GND

TB6612输出引脚：
├── AO1 → 右轮电机正极
├── AO2 → 右轮电机负极
├── BO1 → 左轮电机正极
└── BO2 → 左轮电机负极
```

### **第四步：MPU6050陀螺仪接线**
```
MPU6050接线：
├── VCC → 3.3V
├── GND → GND
├── SCL → MCU A1
├── SDA → MCU A0
└── INT → MCU A7
```

### **第五步：OLED显示屏接线**
```
OLED接线：
├── VCC → 3.3V
├── GND → GND
├── SCL → MCU A28
├── SDA → MCU A31
├── RES → MCU B14
└── DC → MCU B15
```

### **第六步：循迹传感器接线**
```
循迹传感器阵列：
├── 传感器1 → MCU A2
├── 传感器2 → MCU A3
├── 传感器3 → MCU A4
├── 传感器4 → MCU A5
├── 传感器5 → MCU A6
├── VCC → 3.3V
└── GND → GND
```

---

## 📊 系统架构总览

### **完整系统连接图**
```
MSP430 MSPM0 微控制器
├── 🔄 编码器系统
│   ├── 右轮编码器 (A25, A26)
│   └── 左轮编码器 (B20, B24)
├── ⚡ 电机控制系统
│   ├── TB6612驱动器
│   │   ├── 控制引脚 (A16, A17, A14, A13)
│   │   ├── PWM引脚 (B2, B3)
│   │   ├── 使能引脚 (STBY → 5V)
│   │   └── 输出引脚 (AO1, AO2, BO1, BO2)
│   └── 直流电机 (左轮, 右轮)
├── 📡 传感器系统
│   ├── MPU6050陀螺仪 (A0, A1, A7)
│   └── 循迹传感器阵列 (A2-A6)
├── 📺 显示系统
│   └── OLED屏幕 (A28, A31, B14, B15)
├── 🔧 调试系统
│   ├── 用户LED (B9)
│   ├── 用户按键 (A18)
│   ├── 电压监测 (A15)
│   └── UART调试 (A10, A11)
└── ⚡ 电源系统
    ├── 3.3V (MCU、传感器)
    ├── 5V (TB6612逻辑)
    ├── 电机电源 (5V-12V)
    └── 共地 (GND)
```

---

## ✅ 验证检查清单

### **接线验证步骤**
- [ ] 编码器A相B相接线正确 (右轮A25/A26, 左轮B20/B24)
- [ ] TB6612控制引脚接线正确 (AIN1→A16, AIN2→A17, BIN1→A14, BIN2→A13)
- [ ] TB6612 PWM引脚接线正确 (PWMA→B2, PWMB→B3)
- [ ] TB6612 STBY引脚接5V
- [ ] TB6612输出引脚连接电机 (AO1/AO2→右轮, BO1/BO2→左轮)
- [ ] MPU6050 I2C接线正确 (SCL→A1, SDA→A0, INT→A7)
- [ ] OLED I2C接线正确 (SCL→A28, SDA→A31, RES→B14, DC→B15)
- [ ] 循迹传感器接线正确 (A2-A6)
- [ ] 电源连接正确 (3.3V, 5V, GND)
- [ ] 所有模块共地连接

### **功能测试验证**
- [ ] 编码器信号正常
- [ ] 电机正反转控制正常
- [ ] MPU6050数据读取正常
- [ ] OLED显示正常
- [ ] 循迹传感器检测正常
- [ ] 串口调试通信正常

---

## 🎯 总结

**本配置文档提供了小车循迹控制系统的完整标准接线方案。所有引脚配置均已与代码验证匹配，按此文档接线可确保系统正常运行。**

**关键要点：**
1. **TB6612的STBY引脚直接接5V，实现永久使能**
2. **所有I2C设备使用不同的引脚组避免冲突**
3. **编码器和电机控制引脚已优化分配**
4. **电源系统采用分级供电确保稳定性**

---

**文档状态**: ✅ **最终验证完成，可直接使用**
