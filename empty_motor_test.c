/*
 * 纯净电机测试版本
 * 只保留最基本的电机驱动功能，避免所有可能的阻塞
 */

#include "ti_msp_dl_config.h"
#include "bsp_systick.h"

// 电机控制变量
int Turn1 = 0;
int Velocity1 = 0;
uint8_t Flag_Stop = 0;

// 测试变量
int KEY0 = 1;
static int motor_test_state = 0;

// 简单的电机控制函数
void Motor_Control(int velocity, int turn)
{
    int Motor_Left = velocity - turn;
    int Motor_Right = velocity + turn;
    
    // 限制PWM值范围
    if(Motor_Left > 100) Motor_Left = 100;
    if(Motor_Left < -100) Motor_Left = -100;
    if(Motor_Right > 100) Motor_Right = 100;
    if(Motor_Right < -100) Motor_Right = -100;
    
    // 左轮控制 (B端)
    if(Motor_Left >= 0) {
        // 正转
        DL_GPIO_clearPins(MOTOR_DIR_BIN1_PORT, MOTOR_DIR_BIN1_PIN);
        DL_GPIO_setPins(MOTOR_DIR_BIN2_PORT, MOTOR_DIR_BIN2_PIN);
        DL_TimerA_setCaptureCompareValue(PWM_0_INST, Motor_Left, DL_TIMER_CC_1_INDEX);
    } else {
        // 反转
        DL_GPIO_setPins(MOTOR_DIR_BIN1_PORT, MOTOR_DIR_BIN1_PIN);
        DL_GPIO_clearPins(MOTOR_DIR_BIN2_PORT, MOTOR_DIR_BIN2_PIN);
        DL_TimerA_setCaptureCompareValue(PWM_0_INST, -Motor_Left, DL_TIMER_CC_1_INDEX);
    }
    
    // 右轮控制 (A端)
    if(Motor_Right >= 0) {
        // 正转
        DL_GPIO_clearPins(MOTOR_DIR_AIN1_PORT, MOTOR_DIR_AIN1_PIN);
        DL_GPIO_setPins(MOTOR_DIR_AIN2_PORT, MOTOR_DIR_AIN2_PIN);
        DL_TimerA_setCaptureCompareValue(PWM_0_INST, Motor_Right, DL_TIMER_CC_0_INDEX);
    } else {
        // 反转
        DL_GPIO_setPins(MOTOR_DIR_AIN1_PORT, MOTOR_DIR_AIN1_PIN);
        DL_GPIO_clearPins(MOTOR_DIR_AIN2_PORT, MOTOR_DIR_AIN2_PIN);
        DL_TimerA_setCaptureCompareValue(PWM_0_INST, -Motor_Right, DL_TIMER_CC_0_INDEX);
    }
}

int main(void)
{
    SYSCFG_DL_init();

    // 启动调试定时器
    DL_TimerG_startCounter(DebugTimer_INST);
    
    // 启动PWM定时器 - 电机控制必需
    DL_TimerA_startCounter(PWM_0_INST);

    // 串口调试中断 - 保留用于调试
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);

    uint32_t LEDTick = 0;
    
    // 初始化电机状态
    Turn1 = 0;
    Velocity1 = 20;  // 默认前进速度
    Flag_Stop = 0;

    while (1) {
        // 电机控制逻辑
        if(!Flag_Stop) {
            Motor_Control(Velocity1, Turn1);
        } else {
            Motor_Control(0, 0);  // 停止
        }
        
        // 100ms任务
        if(((LEDTick-Systick_getTick())&SysTickMAX_COUNT) >= SysTick_MS(100)) {
            // 读取按键状态
            KEY0 = DL_GPIO_readPins(GPIO_GRP_0_KEY0_PORT, GPIO_GRP_0_KEY0_PIN);
            
            // LED闪烁表示系统正常
            DL_GPIO_togglePins(LED_PORT, LED_UserLED_PIN);
            LEDTick = Systick_getTick();
            
            // 按键控制电机测试
            if(KEY0 == 0) {
                motor_test_state++;
                if(motor_test_state > 5) motor_test_state = 0;
                
                switch(motor_test_state) {
                    case 0: 
                        Velocity1 = 0; Turn1 = 0; Flag_Stop = 1;
                        break;    // 停止
                    case 1: 
                        Velocity1 = 30; Turn1 = 0; Flag_Stop = 0;
                        break;   // 前进
                    case 2: 
                        Velocity1 = -30; Turn1 = 0; Flag_Stop = 0;
                        break;  // 后退
                    case 3: 
                        Velocity1 = 0; Turn1 = 40; Flag_Stop = 0;
                        break;   // 左转
                    case 4: 
                        Velocity1 = 0; Turn1 = -40; Flag_Stop = 0;
                        break;  // 右转
                    case 5: 
                        Velocity1 = 20; Turn1 = 20; Flag_Stop = 0;
                        break;  // 前进+左转
                }
                
                // 防抖延时
                delay_ms(300);
            }
        }
    }
}

// 串口0中断处理 - 用于调试
void UART_0_INST_IRQHandler(void)
{
    switch (DL_UART_Main_getPendingInterrupt(UART_0_INST)) {
        case DL_UART_IIDX_RX:
            DL_UART_transmitData(UART_0_INST, DL_UART_Main_receiveData(UART_0_INST));
            break;
        default:
            break;
    }
}
