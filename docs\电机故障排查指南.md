# 🔧 电机故障排查指南

## 🚨 **当前状况**
- ✅ 代码编译成功
- ❌ 电机完全不动
- ❓ 需要确定是硬件还是软件问题

## 📋 **系统性排查步骤**

### **第一步：确认系统运行状态**
1. **观察LED状态**
   - ✅ LED每100ms闪烁 → 系统正常运行
   - ❌ LED不闪烁 → 系统卡死，检查代码

2. **检查串口输出**
   - 连接UART0 (A10-TX, A11-RX) 到串口助手
   - 波特率：9600
   - 应该看到调试信息：
     ```
     Motor ON: V=20, T=0
     Motor_Control: L=20, R=20
     ```

### **第二步：按键测试**
1. **按下KEY0 (A8引脚)**
   - 应该看到串口输出：
     ```
     KEY pressed! State=1
     Mode: FORWARD
     Motor_Control: L=30, R=30
     ```

2. **多次按键测试**
   - 每次按键应该切换模式
   - 串口应该显示不同的模式信息

### **第三步：硬件连接检查**

#### **🔌 TB6612连接验证**
| 功能 | MCU引脚 | TB6612引脚 | 检查方法 |
|------|---------|------------|----------|
| **右轮方向1** | **A16** | **AIN1** | 万用表测电平 |
| **右轮方向2** | **A17** | **AIN2** | 万用表测电平 |
| **左轮方向1** | **A14** | **BIN1** | 万用表测电平 |
| **左轮方向2** | **A13** | **BIN2** | 万用表测电平 |
| **右轮速度** | **B2** | **PWMA** | 示波器测PWM |
| **左轮速度** | **B3** | **PWMB** | 示波器测PWM |
| **使能** | **5V** | **STBY** | 万用表测5V |

#### **⚡ 电源检查**
1. **TB6612电源**
   - VCC = 3.3V (逻辑电源)
   - VM = 电机电源电压 (通常7-12V)
   - GND = 共地

2. **电机电源**
   - 检查电机电源是否充足
   - 电流是否足够 (每个电机至少1A)

### **第四步：信号测试**

#### **🔍 用万用表测试GPIO**
运行程序后，测量以下引脚电平：

**前进模式时 (Velocity1=30, Turn1=0)**：
- A16 (AIN1) = 0V (LOW)
- A17 (AIN2) = 3.3V (HIGH) 
- A14 (BIN1) = 0V (LOW)
- A13 (BIN2) = 3.3V (HIGH)

**停止模式时**：
- 所有方向引脚应该为0V

#### **📊 用示波器测试PWM**
- B2 (PWMA) 应该有PWM波形
- B3 (PWMB) 应该有PWM波形
- 频率：通常1-20kHz
- 占空比：与速度值成正比

### **第五步：逐步隔离测试**

#### **🧪 最简单测试**
如果以上都正常但电机还是不动，添加以下测试代码：

```c
// 在main函数中添加最简单的测试
while(1) {
    // 强制设置方向引脚
    DL_GPIO_setPins(GPIOA, MOTOR_DIR_AIN2_PIN);    // 右轮正转
    DL_GPIO_clearPins(GPIOA, MOTOR_DIR_AIN1_PIN);
    DL_GPIO_setPins(GPIOA, MOTOR_DIR_BIN2_PIN);    // 左轮正转  
    DL_GPIO_clearPins(GPIOA, MOTOR_DIR_BIN1_PIN);
    
    // 强制设置PWM
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 50, DL_TIMER_CC_0_INDEX);
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 50, DL_TIMER_CC_1_INDEX);
    
    delay_ms(2000);  // 持续2秒
    
    // 停止
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, DL_TIMER_CC_0_INDEX);
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, DL_TIMER_CC_1_INDEX);
    
    delay_ms(2000);  // 停止2秒
}
```

### **第六步：常见问题排查**

#### **❌ 可能的问题**
1. **TB6612未使能**
   - STBY引脚没有连接到5V
   - STBY引脚连接错误

2. **电机电源不足**
   - VM电压太低
   - 电流不够
   - 电源线接触不良

3. **PWM配置错误**
   - PWM频率设置不当
   - PWM通道配置错误

4. **GPIO配置错误**
   - 引脚复用配置错误
   - 方向控制逻辑反了

5. **电机本身问题**
   - 电机损坏
   - 电机卡死
   - 电机连线断开

### **第七步：快速验证方法**

#### **🔥 直接电机测试**
1. **断开TB6612**
2. **直接给电机供电**
   - 正极接电源正极
   - 负极接电源负极
   - 电机应该转动

3. **如果电机能转**
   - 问题在TB6612或控制电路
4. **如果电机不转**
   - 问题在电机本身

## 🎯 **快速诊断流程**

```
LED闪烁? 
├─ NO → 检查代码/系统时钟
└─ YES → 串口有输出?
    ├─ NO → 检查串口连接/波特率
    └─ YES → 按键有响应?
        ├─ NO → 检查按键连接(A8)
        └─ YES → GPIO有电平变化?
            ├─ NO → 检查GPIO配置
            └─ YES → PWM有波形?
                ├─ NO → 检查PWM配置
                └─ YES → 检查TB6612和电机硬件
```

## 🚀 **立即行动**

老板，请按以下顺序检查：
1. **LED是否闪烁？**
2. **串口是否有调试输出？**
3. **按KEY0是否有响应？**
4. **用万用表测试GPIO电平**

告诉我结果，我们立即定位问题！
