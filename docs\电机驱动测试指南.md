# 🚗 电机驱动测试指南

## 📋 测试目标
验证TB6612电机驱动模块是否正常工作，排除其他模块干扰。

## 🔧 已注释的模块
为了避免阻塞问题，已暂时注释掉以下模块：

### ❌ 已禁用的模块
- **OLED显示模块** - 避免I2C阻塞
- **MPU6050陀螺仪** - 避免I2C阻塞和系统复位
- **编码器中断** - 简化测试
- **ADC电压检测** - 简化测试
- **循迹传感器** - 简化测试
- **蓝牙通信** - 简化测试

### ✅ 保留的模块
- **PWM定时器** - 电机速度控制必需
- **GPIO控制** - 电机方向控制必需
- **UART调试** - 用于调试输出
- **系统时钟** - 基础功能
- **LED指示** - 系统运行状态

## 🎮 电机测试功能

### 按键控制测试
按下KEY0按键可以循环切换电机状态：

1. **状态0**: 停止 (Velocity1=0, Turn1=0)
2. **状态1**: 前进 (Velocity1=20, Turn1=0)
3. **状态2**: 后退 (Velocity1=-20, Turn1=0)
4. **状态3**: 左转 (Velocity1=0, Turn1=30)
5. **状态4**: 右转 (Velocity1=0, Turn1=-30)

### 默认运行状态
- **默认速度**: Velocity1 = 20
- **默认方向**: Turn1 = 0 (直行)
- **停止标志**: Flag_Stop = 0 (不停止)

## 🔌 硬件连接验证

### TB6612电机驱动引脚
| 功能 | MCU引脚 | TB6612引脚 | 状态 |
|------|---------|------------|------|
| **右轮方向1** | **A16** | **AIN1** | ✅ |
| **右轮方向2** | **A17** | **AIN2** | ✅ |
| **左轮方向1** | **A14** | **BIN1** | ✅ |
| **左轮方向2** | **A13** | **BIN2** | ✅ |
| **右轮速度** | **B2** | **PWMA** | ✅ |
| **左轮速度** | **B3** | **PWMB** | ✅ |
| **使能** | **5V** | **STBY** | ✅ |

### 电机连接
| 电机 | TB6612输出 | 说明 |
|------|------------|------|
| **右轮电机** | **AO1, AO2** | 连接右轮电机 |
| **左轮电机** | **BO1, BO2** | 连接左轮电机 |

## 🧪 测试步骤

### 1. 编译和下载
```bash
# 编译项目
# 下载到MCU
```

### 2. 观察LED状态
- **LED闪烁** = 系统正常运行
- **LED不闪烁** = 系统卡死

### 3. 测试电机响应
- **上电后**: 电机应该开始以速度20前进
- **按KEY0**: 电机状态应该循环切换
- **观察电机**: 检查是否有转动

### 4. 调试输出
通过UART0 (A10-TX, A11-RX) 可以输出调试信息。

## 🔍 故障排查

### 电机不转动
1. **检查电源**: 确保电机电源充足
2. **检查连线**: 验证TB6612所有引脚连接
3. **检查PWM**: 用示波器检查B2、B3的PWM输出
4. **检查方向**: 用万用表检查A13、A14、A16、A17的电平

### LED不闪烁
1. **系统卡死**: 可能还有其他阻塞代码
2. **时钟问题**: 检查系统时钟配置
3. **硬件问题**: 检查MCU供电和复位

### 按键无响应
1. **检查KEY0连接**: 确保A8引脚连接正确
2. **检查上拉电阻**: KEY0需要上拉电阻
3. **防抖时间**: 按键防抖时间为500ms

## 📊 预期结果

### 正常情况
- ✅ LED每100ms闪烁一次
- ✅ 电机上电后开始转动
- ✅ 按键可以控制电机状态切换
- ✅ 电机能够前进、后退、左转、右转

### 异常情况
- ❌ LED不闪烁 → 系统卡死
- ❌ 电机不转 → 硬件连接问题
- ❌ 按键无效 → 按键电路问题

## 🚀 下一步计划

电机驱动测试通过后，可以逐步添加其他模块：

1. **第一步**: 添加编码器反馈
2. **第二步**: 添加UART调试输出
3. **第三步**: 添加MPU6050（解决I2C问题后）
4. **第四步**: 添加OLED显示（解决I2C问题后）
5. **第五步**: 添加循迹传感器
6. **第六步**: 完整功能集成

---
**注意**: 这是一个最小化测试版本，专门用于验证电机驱动功能！
